package com.hightop.benyin.iot.application.service;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.yulichang.toolkit.MPJWrappers;
import com.google.common.collect.Lists;
import com.hightop.benyin.customer.domain.service.CustomerContractDomainService;
import com.hightop.benyin.customer.domain.service.CustomerContractGiveDomainService;
import com.hightop.benyin.customer.domain.service.CustomerContractServeDomainService;
import com.hightop.benyin.customer.infrastructure.entity.CustomerContractGive;
import com.hightop.benyin.customer.infrastructure.entity.CustomerContractServe;
import com.hightop.benyin.customer.infrastructure.enums.*;
import com.hightop.benyin.iot.api.dto.query.IotPrintCountQuery;
import com.hightop.benyin.iot.domain.service.IotCounterServiceDomain;
import com.hightop.benyin.iot.domain.service.IotPrintCountServiceDomain;
import com.hightop.benyin.iot.infrastructure.entity.IotCounter;
import com.hightop.benyin.iot.infrastructure.entity.IotPrintCount;
import com.hightop.benyin.iot.infrastructure.enums.IotDataType;
import com.hightop.benyin.machine.infrastructure.entity.Machine;
import com.hightop.benyin.product.infrastructure.entity.ProductDevice;
import com.hightop.benyin.repair.price.domain.service.RepairMonthlyPriceServiceDomain;
import com.hightop.benyin.repair.price.infrastructure.entity.RepairMonthlyPrice;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.common.core.util.ExecutorUtils;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 物联网-打印数量mapper
 *
 * <AUTHOR>
 * @date 2024-05-05 16:29:08
 */
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Service
@Slf4j
public class IotCalculateService {

    IotPrintCountServiceDomain iotPrintCountServiceDomain;
    IotCounterServiceDomain iotCounterServiceDomain;
    RepairMonthlyPriceServiceDomain repairMonthlyPriceServiceDomain;
    CustomerContractDomainService customerContractDomainService;
    CustomerContractGiveDomainService customerContractGiveDomainService;
    CustomerContractServeDomainService customerContractServeDomainService;
    IotPrintReceiptService iotPrintReceiptService;

    /**
     * 抄表数据计算
     *
     * @param iotPointerQuery
     */
    public void calculateCyclePointNum(IotPrintCountQuery iotPointerQuery) {
        if (StringUtils.isNotBlank(iotPointerQuery.getCycle())) {
            LocalDate startLocalDate = LocalDate.of(Integer.parseInt(iotPointerQuery.getCycle().split("-")[0]),
                    Integer.parseInt(iotPointerQuery.getCycle().split("-")[1]), 1);
            LocalDate endLocalDate = startLocalDate.with(TemporalAdjusters.lastDayOfMonth());
            LocalDateTime startTime = startLocalDate.atStartOfDay();
            LocalDateTime endTime = endLocalDate.atTime(23, 59, 59);
            iotPointerQuery.setReportTimeStart(startTime);
            iotPointerQuery.setReportTimeEnd(endTime);
        } else {
            if (Objects.isNull(iotPointerQuery.getReportTimeStart())) {
                //默认上月时间
                LocalDate now = LocalDate.now();
                now = now.minusMonths(1);//月份-1
                // 获取当前月的第一天
                LocalDate firstDay = now.with(TemporalAdjusters.firstDayOfMonth());
                // 获取当前月的最后一天
                LocalDate lastDay = now.with(TemporalAdjusters.lastDayOfMonth());
                LocalDateTime startTime = firstDay.atStartOfDay();
                LocalDateTime endTime = lastDay.atTime(23, 59, 59);
                String yearMonth = lastDay.format(DateTimeFormatter.ofPattern("yyyy-MM"));
                iotPointerQuery.setCycle(yearMonth);
                iotPointerQuery.setReportTimeStart(startTime);
                iotPointerQuery.setReportTimeEnd(endTime);
            }
        }

        //删除历史数据
        List<IotPrintCount> iotPrintCountList = iotPrintCountServiceDomain.list(Wrappers.<IotPrintCount>lambdaQuery()
                .eq(IotPrintCount::getCycle, iotPointerQuery.getCycle())
                .eq(iotPointerQuery.getCustomerId() != null, IotPrintCount::getCustomerId, iotPointerQuery.getCustomerId())
                .eq(iotPointerQuery.getDeviceGroupId() != null, IotPrintCount::getDeviceGroupId, iotPointerQuery.getDeviceGroupId())
        );
        List<String> receiptList = iotPrintCountList.stream().map(IotPrintCount::getReceiptCode).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(iotPrintCountList)) {
            iotPrintCountServiceDomain.removeByIds(iotPrintCountList.stream().map(IotPrintCount::getId).collect(Collectors.toList()));
        }

        //查询所有有效服务合同
        List<CustomerContractServe> customerContractServes = customerContractDomainService.selectEffectiveContractList(
                SerTypeEnums.getServeType(), iotPointerQuery.getReportTimeStart().toLocalDate(), iotPointerQuery.getCustomerId(), iotPointerQuery.getDeviceGroupId());
        
        log.info("查询到有效服务合同总数：{}", customerContractServes.size());
        
        if (CollectionUtils.isEmpty(customerContractServes)) {
            log.error("未找到有效服务合同");
            return;
        }
        List<Long> deviceGroupIds = customerContractServes.stream().filter(v -> v.getDeviceGroupId() != null).map(CustomerContractServe::getDeviceGroupId).distinct().collect(Collectors.toList());
        List<IotPrintCount> customerDeviceGroups = iotCounterServiceDomain.getBasicInfo(deviceGroupIds);
        Map<Long, IotPrintCount> customerDeviceGroupMap = customerDeviceGroups.stream().collect(Collectors.toMap(IotPrintCount::getDeviceGroupId, obj -> obj));

        List<IotPrintCount> saveList = Lists.newArrayList();
        List<Long> deviceGroupIdList = Lists.newArrayList();
        int successCount = 0;
        int failCount = 0;
        int missingDeviceGroupCount = 0;
        int duplicateDeviceGroupCount = 0;
        
        for (CustomerContractServe customerContract : customerContractServes) {
            if (customerContract.getDeviceGroupId() == null) {
                log.error("--客户{}设备组id为空", customerContract.getCustomerId());
                missingDeviceGroupCount++;
                failCount++;
                continue;
            }
            if (deviceGroupIdList.contains(customerContract.getDeviceGroupId())) {
                duplicateDeviceGroupCount++;
                continue;
            }
            iotPointerQuery.setDeviceGroupId(customerContract.getDeviceGroupId());
            iotPointerQuery.setCustomerId(customerContract.getCustomerId());

            //查询基础信息(包含计算器上报基础信息与期初计数器统计id信息)
            log.info("查询基础信息客户{}，设备组{}", customerContract.getCustomerId(), customerContract.getDeviceGroupId());
            IotPrintCount iotPrintCount = this.iotCounterServiceDomain.initIotPointer(iotPointerQuery);

            if (iotPrintCount == null) {
                if(!customerDeviceGroupMap.containsKey(customerContract.getDeviceGroupId())){
                    log.error("设备id不存在{}", customerContract.getDeviceGroupId());
                    failCount++;
                    continue;
                }
                iotPrintCount = customerDeviceGroupMap.get(customerContract.getDeviceGroupId());
                iotPrintCount.setId(null);
                iotPrintCount.setCycle(iotPointerQuery.getCycle());
                iotPrintCount.setDataSource(IotDataType.WORK);
                iotPrintCount.setBeginTime(iotPointerQuery.getReportTimeStart());
                iotPrintCount.setEndTime(iotPointerQuery.getReportTimeEnd());
            }
            iotPrintCount.setContractItemCode(customerContract.getCode());
            iotPrintCount.setCycle(iotPointerQuery.getCycle());
            iotPrintCount.setDataSource(IotDataType.COUNTER);
            this.countCyclePoint(iotPrintCount, customerContract, false);
            deviceGroupIdList.add(customerContract.getDeviceGroupId());
            iotPrintCount.setSettmentStatus(0);
            saveList.add(iotPrintCount);
            successCount++;
        }
        
        // 输出统计信息
        log.info("抄表记录处理完成统计：");
        log.info("  - 总合约数：{}", customerContractServes.size());
        log.info("  - 成功生成记录：{}", successCount);
        log.info("  - 失败记录：{}", failCount);
        log.info("  - 缺失设备组：{}", missingDeviceGroupCount);
        log.info("  - 重复设备组：{}", duplicateDeviceGroupCount);
        log.info("  - 实际保存记录数：{}", saveList.size());
        
        iotPrintCountServiceDomain.saveBatch(saveList);

        ExecutorUtils.doAfterCommit(() -> {
            List<Long> customerIds = saveList.stream().filter(v -> v.getCycleType().equals(PayCycleEnums.MONTH)).map(IotPrintCount::getCustomerId).distinct().collect(Collectors.toList());
            List<String> receiptCodes = saveList.stream().filter(v -> v.getCycleType().equals(PayCycleEnums.MONTH)).map(IotPrintCount::getReceiptCode).distinct().collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(customerIds)) {
                iotPrintReceiptService.generateReceiptByCustomer(customerIds, iotPointerQuery.getCycle(), receiptList);
            }
        });
    }

    /**
     * 计算印量与金额
     *
     * @param item
     * @param customerContract
     */
    public void countCyclePoint(IotPrintCount item, CustomerContractServe customerContract, boolean packageEdit) {

        IotPrintCount iotPrintCount = iotPrintCountServiceDomain.lambdaQuery().lt(IotPrintCount::getCycle, item.getCycle())
                .eq(IotPrintCount::getCustomerId, item.getCustomerId())
                .eq(IotPrintCount::getDeviceGroupId, item.getDeviceGroupId())
                .orderByDesc(IotPrintCount::getCycle)
                .last(" LIMIT 1")
                .one();

        //如果上上月找到数据则取最后一条数据作为新一月数据的起始值 如果是没有说明是第一条数据 则要判断客户签约黑白计数器是否有值
        if (iotPrintCount != null) {
            //这里是上月的截止计数器
            item.setBlackWhiteInception(iotPrintCount.getBlackWhiteCutoff());
            item.setColorInception(iotPrintCount.getColorCutoff());
            item.setFiveColourIncption(iotPrintCount.getFiveColourCutoff());
            if(item.getBeginTime()==null){
                item.setBeginTime(iotPrintCount.getEndTime());
            }
        } else {
            if (item.getFirstId() != null) {
                IotCounter firstCounter = iotCounterServiceDomain.getById(item.getFirstId());
                item.setBlackWhiteInception(firstCounter.getBlackWhiteCounter());
                item.setColorInception(firstCounter.getCyanCounter());
                item.setFiveColourIncption(firstCounter.getFifthCounter());
                if(item.getBeginTime()==null){
                    item.setBeginTime(firstCounter.getUpdatedAt());
                }
            }
        }

        if (item.getLastId() != null) {
            if(item.getBlackWhiteCutoff()==0||item.getColorCutoff()==0){
                IotCounter lastCounter = iotCounterServiceDomain.getById(item.getLastId());
                item.setEndTime(lastCounter.getUpdatedAt());
                if(item.getEndTime()==null){
                    item.setEndTime(lastCounter.getUpdatedAt());
                }
                item.setBlackWhiteCutoff(lastCounter.getBlackWhiteCounter());
                item.setColorCutoff(lastCounter.getCyanCounter());
                item.setFiveColourCutoff(lastCounter.getFifthCounter());
            }
        }

        //印量核算及金额计算
        this.calculateCyclePointAmount(customerContract, packageEdit, item);
    }


    /**
     * 计算印量与金额
     *
     * @param customerContract
     * @param item
     */
    private void calculateCyclePointAmount(CustomerContractServe customerContract, boolean packageEdit, IotPrintCount item) {
        // 重置所有金额字段，避免重复累加
        item.setTotalAmount(BigDecimal.ZERO);
        item.setAmount(BigDecimal.ZERO);
        item.setBlackWhiteAmount(BigDecimal.ZERO);
        item.setColorAmount(BigDecimal.ZERO);
        item.setFiveColourAmount(BigDecimal.ZERO);
        item.setDeductionAmount(BigDecimal.ZERO);
        item.setDeductionPoint(0);
        
        // 合同信息
        item.setSignBlackWhiteCounter(customerContract.getSignBlackWhiteCounter());
        item.setSignColoursCounter(customerContract.getSignColoursCounter());
        item.setSignFiveColoursCounter(customerContract.getSignFiveColoursCounter());
        item.setPriceType(customerContract.getPriceType());
        item.setAccountMode(customerContract.getAccountMode());
        item.setBlackWhitePrice(customerContract.getBlackWhitePrice());
        item.setColorPrice(customerContract.getColorPrice());
        item.setFiveColourPrice(customerContract.getFiveColourPrice());
        item.setColorGuarantee(customerContract.getColorGuaranteeCount());
        item.setBlackGuarantee(customerContract.getBlackGuaranteeCount());
        item.setPricePaperType(customerContract.getPaperType());
        item.setStatementDate(customerContract.getStatementDate());
        item.setCycleType(customerContract.getCycleType());
        item.setMachineNum(customerContract.getMachineNum());
        item.setGuaranteeAmount(customerContract.getGuaranteeAmount());
        item.setGuaranteeCount(customerContract.getGuaranteeCount());
        if (StringUtils.isBlank(item.getMachinePaperType())) {
            item.setMachinePaperType("A4");
        }
        if (StringUtils.isBlank(item.getPricePaperType())) {
            item.setPricePaperType("A4");
        }
        //1.数量及废张计算
        this.calculateWasteNum(item, packageEdit, customerContract);

        // 2.合同中是计价方式 机器本身有计数方式
        countMode(item);

        if (item.getBlackWhitePoint() < 0) {
            item.setBlackWhitePoint(0);
        }
        if (item.getColorPoint() < 0) {
            item.setColorPoint(0);
        }
        if (item.getFiveColourPoint() < 0) {
            item.setFiveColourPoint(0);
        }

        //3.保底判断
        countCyclePoint(item);

        //4.赠送印量计算
        countGivePoint(item, customerContract);

        //5.包量扣减
        this.packageDeduction(customerContract, item);

        //计算打印金额
        if (!item.getOnlyGuarantee()) {
            this.countAmount(customerContract.getCode(), item);
        } else {
            if (customerContract.getGuaranteeAmount() != null) {
                item.setTotalAmount(new BigDecimal(customerContract.getGuaranteeAmount()));
                item.setAmount(item.getTotalAmount());
            }
        }
        if (item.getDerateAmount() != null) {
            item.setAmount(item.getAmount().subtract(item.getDerateAmount()));
        }
        item.setTotalPoint(item.getBlackWhitePoint() + item.getColorPoint() + item.getFiveColourPoint());
    }

    public void calculateWasteNum(IotPrintCount item, boolean packageEdit, CustomerContractServe customerContract) {
        Integer blackWhitePointNum = 0;
        if (item.getBlackWhiteCutoff() != null && item.getBlackWhiteInception() != null) {
            blackWhitePointNum = item.getBlackWhiteCutoff() - item.getBlackWhiteInception();
        }
        Integer colorPointNum = 0;
        if (item.getColorCutoff() != null && item.getColorInception() != null) {
            colorPointNum = item.getColorCutoff() - item.getColorInception();
        }
        Integer fiveColourPointNum = 0;
        if (item.getFiveColourCutoff() != null && item.getFiveColourIncption() != null) {
            fiveColourPointNum = item.getFiveColourCutoff() - item.getFiveColourIncption();
        }

        //-------------------------------------------------废张计算
        if (packageEdit) {
            //表示手填废张数据
            blackWhitePointNum = blackWhitePointNum - item.getBlackWhiteExclude();
            colorPointNum = colorPointNum - item.getColorExclude();
            fiveColourPointNum = fiveColourPointNum - item.getFiveColourExclude();
        } else {
            if (customerContract.getWasteType() != null) {
                switch (customerContract.getWasteType()) {
                    case FIXED:
                        //废纸张数
                        blackWhitePointNum = blackWhitePointNum - customerContract.getBwWasteNumber();
                        colorPointNum = colorPointNum - customerContract.getColorWasteNumber();
                        fiveColourPointNum = fiveColourPointNum - customerContract.getFiveWasteNumber();
                        item.setBlackWhiteExclude(customerContract.getBwWasteNumber());
                        item.setColorExclude(customerContract.getColorWasteNumber());
                        item.setFiveColourExclude(customerContract.getFiveWasteNumber());
                        break;
                    case SCALE:
                        //废纸张百分比
                        if (customerContract.getBwWasteScale() != null && customerContract.getBwWasteScale() > 0) {
                            BigDecimal bwWastePercent = new BigDecimal(customerContract.getBwWasteScale()).divide(new BigDecimal(1000), 3, BigDecimal.ROUND_HALF_UP);
                            BigDecimal blackWhitePointNumPercent = new BigDecimal(blackWhitePointNum).multiply(bwWastePercent);
                            item.setBlackWhiteExclude(blackWhitePointNumPercent.intValue());
                            blackWhitePointNum = blackWhitePointNum - blackWhitePointNumPercent.intValue();
                        }
                        if (customerContract.getColorWasteScale() != null && customerContract.getColorWasteScale() > 0) {
                            BigDecimal colorWastePercent = new BigDecimal(customerContract.getColorWasteScale()).divide(new BigDecimal(1000), 3, BigDecimal.ROUND_HALF_UP);
                            BigDecimal colorPointNumPercent = new BigDecimal(colorPointNum).multiply(colorWastePercent);
                            item.setColorExclude(colorPointNumPercent.intValue());
                            colorPointNum = colorPointNum - colorPointNumPercent.intValue();
                        }
                        if (customerContract.getFiveWasteScale() != null && customerContract.getFiveWasteScale() > 0) {
                            BigDecimal fiveWastePercent = new BigDecimal(customerContract.getFiveWasteScale()).divide(new BigDecimal(1000), 3, BigDecimal.ROUND_HALF_UP);
                            BigDecimal fivePointNumPercent = new BigDecimal(fiveColourPointNum).multiply(fiveWastePercent);
                            item.setFiveColourExclude(fivePointNumPercent.intValue());
                            fiveColourPointNum = fiveColourPointNum - fivePointNumPercent.intValue();
                        }
                        break;
                    case ACTUAL:
                        //实际
                        blackWhitePointNum = blackWhitePointNum - item.getBlackWhiteExclude();
                        colorPointNum = colorPointNum - item.getColorExclude();
                        fiveColourPointNum = fiveColourPointNum - item.getFiveColourExclude();
                        break;
                }
            }
        }

        //彩色打印数量
        item.setColorPoint(colorPointNum < 0 ? 0 : colorPointNum);
        //黑白打印数量
        item.setBlackWhitePoint(blackWhitePointNum < 0 ? 0 : blackWhitePointNum);
        //五色打印数量
        item.setFiveColourPoint(fiveColourPointNum < 0 ? 0 : fiveColourPointNum);
    }


    /**
     * 获取计数方式判断
     */
    private void countMode(IotPrintCount item) {
        if ((StringUtils.isNotBlank(item.getMachinePaperType()) && item.getMachinePaperType().equals("A3"))
                && (StringUtils.isNotBlank(item.getPricePaperType()) && item.getPricePaperType().equals("A4"))) {
            item.setColorPoint(item.getColorPoint() * 2);
            item.setBlackWhitePoint(item.getBlackWhitePoint() * 2);
            item.setFiveColourPoint(item.getFiveColourPoint() * 2);
        }

        if ((StringUtils.isNotBlank(item.getMachinePaperType()) && item.getMachinePaperType().equals("A4"))
                && (StringUtils.isNotBlank(item.getPricePaperType()) && item.getPricePaperType().equals("A3"))) {
            item.setColorPoint(item.getColorPoint() / 2);
            item.setBlackWhitePoint(item.getBlackWhitePoint() / 2);
            item.setFiveColourPoint(item.getFiveColourPoint() / 2);
        }
    }

    /**
     * 保底判断
     *
     * @param item
     */
    private void countCyclePoint(IotPrintCount item) {
        Integer colorGuarantee = item.getColorGuarantee() != null ? item.getColorGuarantee() : 0;
        Integer blackWhiteGuarantee = item.getBlackGuarantee() != null ? item.getBlackGuarantee() : 0;
        if (item.getColorPoint() <= colorGuarantee && item.getBlackWhitePoint() <= blackWhiteGuarantee) {
            item.setOnlyGuarantee(true);
        }
    }

    /**
     * 包量扣减
     *
     * @param customerContract
     * @param item
     */
    private void packageDeduction(CustomerContractServe customerContract, IotPrintCount item) {
        if (customerContract.getSerType().equals(SerTypeEnums.PACKAGE_ALL)
                || customerContract.getSerType().equals(SerTypeEnums.PACKAGE_HALF)) {

            //说明是包量有效期内
            if (customerContract.getPackageExpireDate()==null ||
                    item.getBeginTime().toLocalDate().isBefore(customerContract.getPackageExpireDate())) {
                //全部包量
                Integer quantity = customerContract.getPackageNumber();
                Integer usedPoint = iotPrintCountServiceDomain.selectJoinOne(Integer.class, MPJWrappers.lambdaJoin()
                        .selectSum(IotPrintCount::getDeductionPoint)
                        .eq(IotPrintCount::getContractItemCode, customerContract.getCode())
                        .ne(item.getId() != null, IotPrintCount::getId, item.getId())
                );
                usedPoint = usedPoint == null ? 0 : usedPoint;
                //总 印量
                Integer totalPoint = item.getTotalPoint();
                //包量剩余印量
                Integer balance = quantity - usedPoint;
                switch (customerContract.getPackageType()) {
                    case TOTAL:
                        //抵扣印量
                        Integer deductionPoint = balance > totalPoint ? totalPoint : balance;
                        item.setDeductionPoint(deductionPoint);
                        item.setDeductionAmount(customerContract.getPackageBwPrice().multiply(new BigDecimal(deductionPoint)));
                        item.setTotalAmount(item.getDeductionAmount());
                        item.setColorPrice(customerContract.getPackageColorPrice());
                        item.setBlackWhitePrice(customerContract.getPackageBwPrice());
                        customerContract.setPackageUse(usedPoint + deductionPoint);
                        customerContractServeDomainService.updateById(customerContract);
                        if (item.getTotalPoint() > item.getDeductionPoint()) {
                            //使用正常价格
                            Integer normalNumber = item.getTotalPoint() - item.getDeductionPoint();
                            item.setColorPrice(customerContract.getColorPrice());
                            item.setBlackWhitePrice(customerContract.getBlackWhitePrice());
                            item.setAmount(customerContract.getBlackWhitePrice().multiply(new BigDecimal(normalNumber)));
                            item.setTotalAmount(item.getAmount().add(item.getTotalAmount()));
                        }
                        break;
                    case BW:
                        totalPoint = item.getBlackWhitePoint();
                        //抵扣印量
                        Integer deductionBwPoint = balance > totalPoint ? totalPoint : balance;
                        item.setDeductionPoint(deductionBwPoint);
                        item.setDeductionAmount(customerContract.getPackageBwPrice().multiply(new BigDecimal(deductionBwPoint)));
                        item.setTotalAmount(item.getDeductionAmount());
                        item.setColorPrice(customerContract.getColorPrice());
                        item.setBlackWhitePrice(customerContract.getPackageBwPrice());
                        customerContract.setPackageUse(usedPoint + deductionBwPoint);
                        customerContractServeDomainService.updateById(customerContract);
                        if (item.getBlackWhitePoint() > item.getDeductionPoint()) {
                            //使用正常价格
                            Integer normalNumber = item.getBlackWhitePoint() - item.getDeductionPoint();
                            item.setBlackWhitePrice(customerContract.getBlackWhitePrice());
                            item.setBlackWhiteAmount(customerContract.getBlackWhitePrice().multiply(new BigDecimal(normalNumber)));
                            item.setAmount(item.getBlackWhiteAmount());
                            item.setTotalAmount(item.getAmount().add(item.getTotalAmount()));
                        }
                        if (item.getColorPoint() > 0) {
                            item.setColorPrice(customerContract.getColorPrice());
                            item.setColorAmount(customerContract.getColorPrice().multiply(new BigDecimal(item.getColorPoint())));
                            item.setAmount(item.getAmount().add(item.getColorAmount()));
                            item.setTotalAmount(item.getTotalAmount().add(item.getColorAmount()));
                        }
                        break;
                    case COLOR:
                        totalPoint = item.getColorPoint();
                        //抵扣印量
                        Integer deductionColorPoint = balance > totalPoint ? totalPoint : balance;
                        item.setDeductionPoint(deductionColorPoint);
                        item.setDeductionAmount(customerContract.getPackageColorPrice().multiply(new BigDecimal(deductionColorPoint)));
                        item.setTotalAmount(item.getDeductionAmount());
                        item.setColorPrice(customerContract.getPackageColorPrice());
                        item.setBlackWhitePrice(customerContract.getBlackWhitePrice());
                        customerContract.setPackageUse(usedPoint + deductionColorPoint);
                        customerContractServeDomainService.updateById(customerContract);
                        if (totalPoint > item.getDeductionPoint()) {
                            //使用正常价格
                            Integer normalNumber = totalPoint - item.getDeductionPoint();
                            item.setColorPrice(customerContract.getColorPrice());
                            item.setColorAmount(customerContract.getColorPrice().multiply(new BigDecimal(normalNumber)));
                            item.setAmount(item.getColorAmount());
                            item.setTotalAmount(item.getAmount().add(item.getTotalAmount()));
                        }
                        if (item.getBlackWhitePoint() > 0) {
                            item.setBlackWhiteAmount(item.getBlackWhitePrice().multiply(new BigDecimal(item.getBlackWhitePoint())));
                            item.setAmount(item.getAmount().add(item.getBlackWhiteAmount()));
                            item.setTotalAmount(item.getTotalAmount().add(item.getBlackWhiteAmount()));
                        }
                        break;
                }
                if (item.getDeductionPoint() != null && item.getDeductionPoint() > 0) {
                    Integer totalUsePoint = item.getDeductionPoint() + usedPoint;
                    customerContractServeDomainService.lambdaUpdate()
                            .set(CustomerContractServe::getPackageUse, totalUsePoint)
                            .eq(CustomerContractServe::getCode, customerContract.getCode())
                            .update();
                }
            } else {
                //使用正常价格
                item.setColorPrice(customerContract.getColorPrice());
                item.setBlackWhitePrice(customerContract.getBlackWhitePrice());
                item.setBlackWhiteAmount(item.getBlackWhitePrice().multiply(new BigDecimal(item.getBlackWhitePoint())));
                item.setColorAmount(item.getColorPrice().multiply(new BigDecimal(item.getColorPoint())));
                item.setAmount(item.getColorAmount().add(item.getBlackWhiteAmount()));
                item.setTotalAmount(item.getAmount());
            }

            if (item.getFiveColourPoint() > 0) {
                item.setFiveColourPrice(customerContract.getFiveColourPrice());
                item.setFiveColourAmount(customerContract.getColorPrice().multiply(new BigDecimal(item.getFiveColourPoint())));
                item.setAmount(item.getAmount().add(item.getFiveColourAmount()));
                item.setTotalAmount(item.getTotalAmount().add(item.getFiveColourAmount()));
            }

        }
    }

    /**
     * 计算赠送印量
     *
     * @param iotPrintCount
     * @param customerContract
     */
    private void countGivePoint(IotPrintCount iotPrintCount, CustomerContractServe customerContract) {
        if (!customerContract.getHasGive()) {
            return;
        }
        List<CustomerContractGive> customerContractGives = customerContractGiveDomainService.getEffectiveList(iotPrintCount.getDeviceGroupId(), iotPrintCount.getBeginTime().toLocalDate());
        customerContractGives = customerContractGives.stream().filter(c ->
                        c.getGiveType().equals(GiveType.PRINT) || c.getGiveType().equals(GiveType.BLACK_PRINT) || c.getGiveType().equals(GiveType.COLOR_PRINT))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(customerContractGives)) {
            return;
        }
        //已赠送印量
        List<IotPrintCount> iotPrintCountList = iotPrintCountServiceDomain.lambdaQuery()
                .lt(IotPrintCount::getCycle, iotPrintCount.getCycle())
                .eq(IotPrintCount::getCustomerId, iotPrintCount.getCustomerId())
                .eq(IotPrintCount::getDeviceGroupId, iotPrintCount.getDeviceGroupId()).list();
        Long blackGiveCount = iotPrintCountList.stream().filter(c -> c.getGiveBlackCount() != null && c.getGiveBlackCount() > 0).mapToLong(IotPrintCount::getGiveBlackCount).sum();
        Long colorGiveCount = iotPrintCountList.stream().filter(c -> c.getGiveColorCount() != null && c.getGiveColorCount() > 0).mapToLong(IotPrintCount::getGiveBlackCount).sum();
        Long totalGiveCount = iotPrintCountList.stream().filter(c -> c.getGiveCount() != null && c.getGiveCount() > 0).mapToLong(IotPrintCount::getGiveBlackCount).sum();
        for (CustomerContractGive customerContractGive : customerContractGives) {
            Integer count = customerContractGive.getQuantity();

            if (customerContractGive.getGiveType().equals(GiveType.BLACK_PRINT)) {
                if (blackGiveCount != null) {
                    count = count - blackGiveCount.intValue();
                }
                if (count <= 0) {
                    continue;
                }
                if (iotPrintCount.getBlackWhitePoint().compareTo(count) >= 0) {
                    iotPrintCount.setGiveBlackCount(count);
                } else {
                    iotPrintCount.setGiveBlackCount(iotPrintCount.getBlackWhitePoint());
                }
                iotPrintCount.setBlackWhitePoint(iotPrintCount.getBlackWhitePoint() - iotPrintCount.getGiveBlackCount());
                iotPrintCount.setTotalPoint(iotPrintCount.getTotalPoint() - iotPrintCount.getGiveBlackCount());
            }

            if (customerContractGive.getGiveType().equals(GiveType.COLOR_PRINT)) {
                if (colorGiveCount != null) {
                    count = count - colorGiveCount.intValue();
                }
                if (count <= 0) {
                    continue;
                }
                if (iotPrintCount.getColorPoint().compareTo(count) >= 0) {
                    iotPrintCount.setGiveColorCount(count);
                } else {
                    iotPrintCount.setGiveColorCount(iotPrintCount.getColorPoint());
                }
                iotPrintCount.setColorPoint(iotPrintCount.getColorPoint() - iotPrintCount.getGiveColorCount());
                iotPrintCount.setTotalPoint(iotPrintCount.getTotalPoint() - iotPrintCount.getGiveColorCount());

            }

            if (customerContractGive.getGiveType().equals(GiveType.PRINT)) {
                if (totalGiveCount != null) {
                    count = count - totalGiveCount.intValue();
                }
                if (count <= 0) {
                    continue;
                }
                if (iotPrintCount.getTotalPoint().compareTo(count) >= 0) {
                    iotPrintCount.setGiveCount(count);
                } else {
                    iotPrintCount.setGiveCount(iotPrintCount.getTotalPoint());
                }
                iotPrintCount.setTotalPoint(iotPrintCount.getTotalPoint() - iotPrintCount.getGiveCount());
            }
        }
    }

    private void countAmount(String contractItemCode, IotPrintCount item) {
        //包量不再重复计算
        if (item.getSerType().getCode().equals(SerTypeEnums.PACKAGE_ALL.getCode())
                || item.getSerType().getCode().equals(SerTypeEnums.PACKAGE_HALF.getCode())) {
            return;
        }
        AccountModeEnums accountMode = item.getAccountMode();
        //固定价
        if (item.getPriceType().equals(PriceTypeEnums.FIXED)) {
            if (item.getBlackWhitePoint() != null && item.getBlackWhitePoint() > 0) {
                Integer count = item.getBlackWhitePoint();
                if (item.getBlackGuarantee() != null && item.getBlackGuarantee() > 0) {
                    if (count > item.getBlackGuarantee()) {
                        count = count - item.getBlackGuarantee();
                        BigDecimal amount = BigDecimal.ZERO;
                        if (count > 0) {
                            amount = new BigDecimal(count).multiply(item.getBlackWhitePrice());
                            BigDecimal guaranteeAmount = item.getGuaranteeAmount() != null ? new BigDecimal(item.getGuaranteeAmount()) : BigDecimal.ZERO;
                            item.setBlackWhiteAmount(amount.add(guaranteeAmount));
                        }
                    }
                } else {
                    BigDecimal amount = new BigDecimal(count).multiply(item.getBlackWhitePrice());
                    item.setBlackWhiteAmount(amount);
                }
                item.setTotalAmount(item.getTotalAmount().add(item.getBlackWhiteAmount()));
            }
            if (item.getColorPoint() != null && item.getColorPoint() > 0) {
                Integer count = item.getColorPoint();
                if (item.getColorGuarantee() != null && item.getColorGuarantee() > 0) {
                    if (count > item.getColorGuarantee()) {
                        count = count - item.getColorGuarantee();
                        BigDecimal amount = BigDecimal.ZERO;
                        if (count > 0) {
                            amount = new BigDecimal(count).multiply(item.getColorPrice());
                            BigDecimal guaranteeAmount = item.getGuaranteeAmount() != null ? new BigDecimal(item.getGuaranteeAmount()) : BigDecimal.ZERO;
                            item.setColorAmount(amount.add(guaranteeAmount));
                        }
                    }
                } else {
                    BigDecimal amount = new BigDecimal(count).multiply(item.getColorPrice());
                    item.setColorAmount(amount);
                }
                item.setTotalAmount(item.getTotalAmount().add(item.getColorAmount()));

            }
            if (item.getFiveColourPoint() != null && item.getFiveColourPoint() > 0) {
                BigDecimal fiveColourAmount = new BigDecimal(item.getFiveColourPoint()).multiply(item.getFiveColourPrice());
                item.setFiveColourAmount(fiveColourAmount);
                item.setTotalAmount(item.getTotalAmount().add(item.getFiveColourAmount()));
            }
        } else {
            //阶梯 统一计价
            if (accountMode.equals(AccountModeEnums.UNIFY)) {
                //先找黑白彩色统一计价
                RepairMonthlyPrice repairMonthlyPrice = this.getRepairPrice(contractItemCode, item.getTotalPoint(), Machine.COLOR, item.getDeviceGroupId());
                if(repairMonthlyPrice!=null){
                    Integer count = item.getTotalPoint();
                    //有保底 则减去保底
                    if (item.getGuaranteeCount() != null && item.getGuaranteeCount() > 0) {
                        BigDecimal guaranteeAmount = new BigDecimal(item.getGuaranteeAmount());
                        if (count > item.getBlackGuarantee()) {
                            count = count - item.getBlackGuarantee();
                            BigDecimal amount = new BigDecimal(count).multiply(repairMonthlyPrice.getPrice());
                            item.setBlackWhitePrice(repairMonthlyPrice.getPrice());
                            item.setColorPrice(repairMonthlyPrice.getPrice());
                            item.setTotalAmount(guaranteeAmount.add(amount));
                        }
                    } else {
                        BigDecimal amount = new BigDecimal(count).multiply(repairMonthlyPrice.getPrice());
                        item.setBlackWhitePrice(repairMonthlyPrice.getPrice());
                        item.setColorPrice(repairMonthlyPrice.getPrice());
                        item.setTotalAmount(amount);
                    }
                }else{
                    if (item.getBlackWhitePoint() != null && item.getBlackWhitePoint() > 0) {
                        Integer count = item.getBlackWhitePoint();
                        //有保底 则减去保底
                        if (item.getBlackGuarantee() != null && item.getBlackGuarantee() > 0) {
                            BigDecimal guaranteeAmount = new BigDecimal(item.getGuaranteeAmount());
                            if (count > item.getBlackGuarantee()) {
                                RepairMonthlyPrice price = this.getRepairPrice(contractItemCode, item.getBlackWhitePoint(), Machine.WHITE_BLACK_COLOR, item.getDeviceGroupId());
                                count = count - item.getBlackGuarantee();
                                if (price != null) {
                                    BigDecimal amount = new BigDecimal(count).multiply(price.getPrice());
                                    item.setBlackWhitePrice(price.getPrice());
                                    item.setBlackWhiteAmount(guaranteeAmount.add(amount));
                                }
                            }
                        } else {
                            RepairMonthlyPrice price = this.getRepairPrice(contractItemCode, item.getBlackWhitePoint(), Machine.WHITE_BLACK_COLOR, item.getDeviceGroupId());
                            if (price != null) {
                                BigDecimal amount = new BigDecimal(item.getBlackWhitePoint()).multiply(price.getPrice());
                                item.setBlackWhitePrice(price.getPrice());
                                item.setBlackWhiteAmount(amount);
                            }
                        }
                        item.setTotalAmount(item.getTotalAmount().add(item.getBlackWhiteAmount()));

                    }
                    if (item.getColorPoint() != null && item.getColorPoint() > 0) {

                        Integer count = item.getColorPoint();
                        //有保底 则减去保底
                        if (item.getColorGuarantee() != null && item.getColorGuarantee() > 0) {
                            BigDecimal guaranteeAmount = new BigDecimal(item.getGuaranteeAmount());
                            if (count > item.getColorGuarantee()) {
                                RepairMonthlyPrice price = this.getRepairPrice(contractItemCode, item.getColorPoint(), Machine.CHROMA_COLOR, item.getDeviceGroupId());
                                count = count - item.getColorGuarantee();
                                if (price != null) {
                                    BigDecimal amount = new BigDecimal(count).multiply(price.getPrice());
                                    item.setColorPrice(price.getPrice());
                                    item.setColorAmount(amount.add(guaranteeAmount));
                                }
                            }
                        } else {
                            RepairMonthlyPrice price = this.getRepairPrice(contractItemCode, item.getColorPoint(), Machine.CHROMA_COLOR, item.getDeviceGroupId());
                            if (price != null) {
                                BigDecimal amount = new BigDecimal(item.getColorPoint()).multiply(price.getPrice());
                                item.setColorPrice(price.getPrice());
                                item.setColorAmount(amount);
                            }
                        }
                        item.setTotalAmount(item.getTotalAmount().add(item.getColorAmount()));
                    }
                }
                //五色单计算
                if (item.getFiveColourPoint() != null && item.getFiveColourPoint() > 0) {
                    RepairMonthlyPrice price = this.getRepairPrice(contractItemCode, item.getFiveColourPoint(), Machine.FIVE_COLOUR, item.getDeviceGroupId());
                    if (price != null) {
                        BigDecimal fiveColourAmount = new BigDecimal(item.getFiveColourPoint()).multiply(price.getPrice());
                        item.setFiveColourPrice(price.getPrice());
                        item.setFiveColourAmount(fiveColourAmount);
                        item.setTotalAmount(item.getTotalAmount().add(item.getFiveColourAmount()));
                    }
                }
            }
            //阶梯 分段计价
            if (accountMode.equals(AccountModeEnums.FLOAT)) {
                List<RepairMonthlyPrice> repairMonthlyPrices = repairMonthlyPriceServiceDomain.lambdaQuery()
                        .eq(RepairMonthlyPrice::getDeviceGroupId, item.getDeviceGroupId())
                        .orderByAsc(RepairMonthlyPrice::getStartCount)
                        .list();
                List<RepairMonthlyPrice> totalPrices = repairMonthlyPrices.stream().filter(p -> p.getColorType().equals(Machine.COLOR)).collect(Collectors.toList());
                if(CollectionUtils.isNotEmpty(totalPrices)){
                    //总印量价格
                    item.setTotalAmount(this.countAmount(item.getDeviceGroupId(), ProductDevice.COLOR, item.getTotalPoint(), totalPrices));
                }else{
                    List<RepairMonthlyPrice> blackWhitePrices = repairMonthlyPrices.stream().filter(p -> p.getColorType().equals(Machine.WHITE_BLACK_COLOR)).collect(Collectors.toList());
                    List<RepairMonthlyPrice> colorPrices = repairMonthlyPrices.stream().filter(p -> p.getColorType().equals(Machine.CHROMA_COLOR)).collect(Collectors.toList());
                    item.setBlackWhiteAmount(this.countAmount(item.getDeviceGroupId(), ProductDevice.WHITE_BLACK_COLOR, item.getBlackWhitePoint(), blackWhitePrices));
                    item.setColorAmount(this.countAmount(item.getDeviceGroupId(), ProductDevice.CHROMA_COLOR, item.getColorPoint(), colorPrices));
                    item.setTotalAmount(item.getColorAmount().add(item.getBlackWhiteAmount()));
                }
                //五色单计算
                if(item.getFiveColourPoint()!=null && item.getFiveColourPoint() > 0){
                    List<RepairMonthlyPrice> fivePrices = repairMonthlyPrices.stream().filter(p -> p.getColorType().equals(Machine.FIVE_COLOUR)).collect(Collectors.toList());
                    item.setFiveColourAmount(this.countAmount(item.getDeviceGroupId(), ProductDevice.FIVE_COLOUR, item.getFiveColourPoint(), fivePrices));
                    item.setTotalAmount(item.getTotalAmount().add(item.getFiveColourAmount()));
                }
            }
        }
        item.setAmount(item.getTotalAmount());

    }

    /**
     * 获取价格
     *
     * @param printCount
     * @param deviceGroupId
     * @param contractItemCode 合同id
     * @return
     */
    private RepairMonthlyPrice getRepairPrice(String contractItemCode, Integer printCount, String colorType, Long deviceGroupId) {
        RepairMonthlyPrice repairMonthlyPrice = repairMonthlyPriceServiceDomain.lambdaQuery()
                .eq(RepairMonthlyPrice::getDeviceGroupId, deviceGroupId)
                .eq(RepairMonthlyPrice::getItemCode, contractItemCode)
                .eq(RepairMonthlyPrice::getColorType, colorType)
                .last(" and " + printCount + " between start_count and end_count order by end_count asc limit 1 ").one();
        if (repairMonthlyPrice == null) {
            log.error("设备组{}，色彩类型{}，印量{}未找到价格", deviceGroupId, colorType, printCount);
            return null;
        }
        return repairMonthlyPrice;
    }

    /**
     * 分段计算总金额
     *
     * @param colorType
     * @param printCount
     * @return
     */
    private BigDecimal countAmount(Long deviceGroupId, String colorType, Integer printCount, List<RepairMonthlyPrice> repairMonthlyPrices) {
        if (printCount <= 0L) {
            log.error("分段计算设备组{}，色彩类型{}，印量为0", deviceGroupId, colorType);
            return BigDecimal.ZERO;
        }
        if (CollectionUtils.isEmpty(repairMonthlyPrices)) {
            log.error("分段计算设备组{}，色彩类型{}，未找到价格", deviceGroupId, colorType);
            return BigDecimal.ZERO;
        }
        BigDecimal amount = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(repairMonthlyPrices)) {
            for (int i = 0; i < repairMonthlyPrices.size(); i++) {
                RepairMonthlyPrice repairMonthlyPrice = repairMonthlyPrices.get(i);
                BigDecimal price = repairMonthlyPrice.getPrice();
                //本次核算数量
                Integer count = repairMonthlyPrice.getEndCount() - repairMonthlyPrice.getStartCount();
                if (printCount >= count) {
                    amount = amount.add(price.multiply(new BigDecimal(count)));
                    printCount = printCount - count;
                } else {
                    amount = amount.add(price.multiply(new BigDecimal(printCount)));
                    break;
                }
                //超过部分用最后的价格计算
                if (i == repairMonthlyPrices.size() - 1) {
                    if (printCount > 0L) {
                        amount = amount.add(price.multiply(new BigDecimal(printCount)));
                    }
                }
            }
        }
        return amount;
    }


}
