<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hightop.benyin.replace.infrastructure.mapper.ReplaceDetailMapper">

    <select id="getApplyUseDetailsList" resultType="com.hightop.benyin.statistics.application.vo.ApplyUseDetailVO">
        select t.*
        from (select distinct t.id,
        bc.seq_id               customerSeqId,
        bc.name     customerName,
        p.full_id_path          fullIdPath,
        p.name                  machine,
        g.device_group          deviceGroup,
        g.machine_num           machineNum,
        t2.label                deviceGroupName,
        ifnull(ti.code ,item.code)                itemCode,
        ifnull(sa.name,t.item_name) name,
        sa.code,
        sa.number_oem           numberOem,
        sa.manufacturer_channel manufacturerChannel,
        t.num,
        sa.unit,
        g.product_id            productId,
        t.location,
        t.sale_unit_price      saleUnitPrice,
        max(sib.price) costPrice,
        1                       useType,
        tis.sku_source          skuSource,
        rp.black_white_count    blackWhiteCounter,
        rp.color_count          colorCounter,
        t1.created_at           createdAt,
        tu.name                 replacer,
        ts.sku_pic_url          skuPicUrl,
        t1.work_order_code orderCode
        from tb_replace_detail t
        left join tb_item_store tis on tis.id = t.item_store_id
        left join tb_replace_order t1 on t1.id = t.replace_order_id
        left join tb_repair_report rp on rp.work_order_id = t1.work_order_id
        left join b_customer bc on t1.customer_id = bc.id
        left join b_customer_device_group g on g.id = t1.device_group_id
        left join st_dict_item t2 on g.device_group = t2.value and t2.dict_id = 20700
        left join b_product_tree p on p.id = g.product_id
        left join tb_sale_sku ts on ts.id = t.sale_sku_id
        left join tb_item ti on ti.id = ts.item_id
        left join tb_item item on item.id = t.item_id
        left join b_storage_article sa on sa.code = tis.article_code
        left join st_user_basic tu on tu.id = t1.engineer_id
        left join b_storage_inventory_batch sib on sib.code = tis.article_code and sib.batch_code = t.batch_code and sib.deleted = 0
        where t.deleted=0 and t1.deleted=0
        group by t.id
        union all
        select t.id,
        bc.seq_id               customerSeqId,
        bc.name    customerName,
        p.full_id_path          fullIdPath,
        p.name                  machine,
        g.device_group          deviceGroup,
        g.machine_num           machineNum,
        t2.label                deviceGroupName,
        ifnull(ti.code ,item.code)                itemCode,
        ifnull(sa.name,t.item_name) name,
        sa.code,
        sa.number_oem           numberOem,
        sa.manufacturer_channel manufacturerChannel,
        t.num,
        sa.unit,
        g.product_id            productId,
        t.location,
        t.sale_unit_price      saleUnitPrice,
        sib.price costPrice,
        2                       useType,
        tis.sku_source          skuSource,
        t1.black_white_count    blackWhiteCounter,
        t1.color_count          colorCounter,
        t1.created_at           createdAt,
        bc.name                 replacer,
        ts.sku_pic_url          skuPicUrl,
        t1.code orderCode
        from tb_self_repair_report_replace_detail t
        left join tb_item_store tis on tis.id = t.item_store_id
        left join tb_self_repair_report t1 on t1.id = t.self_repair_report_id
        left join b_customer bc on t1.customer_id = bc.id
        left join b_customer_device_group g on g.id = t1.device_group_id
        left join st_dict_item t2 on g.device_group = t2.value and t2.dict_id = 20700
        left join b_product_tree p on p.id = g.product_id
        left join tb_sale_sku ts on ts.id = t.sale_sku_id
        left join tb_item ti on ti.id = ts.item_id
        left join tb_item item on item.id = t.item_id
        left join b_storage_article sa on sa.code = tis.article_code
        left join b_storage_inventory_batch sib on sib.code = tis.article_code and sib.batch_code = t.batch_code and sib.deleted = 0
        where t.deleted=0) t
        where 1=1
        <if test="null != qo.customerSeqId and '' != qo.customerSeqId ">
            and t.customerSeqId like concat ('%',#{qo.customerSeqId},'%')
        </if>

        <if test="null != qo.deviceGroup and '' != qo.deviceGroup ">
            and t.deviceGroupName like concat ('%',#{qo.deviceGroup},'%')
        </if>
        <if test="null != qo.machineNum and '' != qo.machineNum ">
            and t.machine_num like concat ('%',#{qo.machineNum},'%')
        </if>
        <if test="null != qo.customerName and '' != qo.customerName ">
            and t.customerName like concat ('%',#{qo.customerName},'%')
        </if>

        <if test="null != qo.orderCode and '' != qo.orderCode ">
            and t.orderCode like concat ('%',#{qo.orderCode},'%')
        </if>

        <if test="null != qo.itemCode and '' != qo.itemCode ">
            and t.itemCode like concat ('%',#{qo.itemCode},'%')
        </if>
        <if test="null != qo.name and '' != qo.name ">
            and t.name like concat ('%',#{qo.name},'%')
        </if>
        <if test="null != qo.code and '' != qo.code ">
            and t.code like concat ('%',#{qo.code},'%')
        </if>
        <if test="null != qo.numberOem and '' != qo.numberOem ">
            and t.numberOem like concat ('%',#{qo.numberOem},'%')
        </if>

        <if test="null != qo.startDate and '' != qo.startDate ">
            and t.createdAt &gt;= concat(#{qo.startDate},' 00:00:00')
        </if>
        <if test="null != qo.endDate and '' != qo.endDate ">
            and t.createdAt &lt;= concat(#{qo.endDate},' 23:59:59')
        </if>


        <if test="null!=qo.lastIds and !qo.lastIds.isEmpty()">
            and t.productId in
            <foreach collection="qo.lastIds" item="id" separator="," open="(" close=")">
                #{id}
            </foreach>
        </if>
        order by t.id desc
    </select>

</mapper>
