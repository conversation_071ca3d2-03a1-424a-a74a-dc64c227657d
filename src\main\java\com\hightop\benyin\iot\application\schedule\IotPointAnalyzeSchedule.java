package com.hightop.benyin.iot.application.schedule;

import com.hightop.benyin.iot.api.dto.query.IotPrintCountQuery;
import com.hightop.benyin.iot.application.service.IotCalculateService;
import com.hightop.benyin.iot.application.service.IotPrintCountService;
import com.hightop.benyin.iot.application.service.IotPrintReceiptService;
import com.hightop.benyin.order.domain.service.TradeOrderInstallmentDomainService;
import com.hightop.benyin.order.infrastructure.entity.TradeOrderInstallment;
import com.hightop.magina.standard.task.job.ScheduleJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/05/19 19:42
 */
@Component
public class IotPointAnalyzeSchedule {

    private static final Logger logger = LoggerFactory.getLogger(IotPointAnalyzeSchedule.class);

    @Autowired
    private IotCalculateService iotCalculateService;

    @Autowired
    private IotPrintReceiptService iotPrintReceiptService;

    @Autowired
    private TradeOrderInstallmentDomainService tradeOrderInstallmentDomainService;

    /**
     * 每月1号凌晨统计上月客户端上报打印张数
     *
     * @throws Exception
     */
    @ScheduleJob(id = 20003L, cron = "0 0 1 1 * ?", description = "客户端上报打印张数核算")
    public void statisticsExtPrintCount() throws Exception {
        logger.info("统计客户端上报打印张数开始------");
        long startTime = System.currentTimeMillis();
        
        try {
            IotPrintCountQuery query = new IotPrintCountQuery();
            
            // 统计变量
            int totalContracts = 0;
            int successfulRecords = 0;
            int failedRecords = 0;
            int missingDeviceGroups = 0;
            String cycle = "";
            
            // 执行计算
            iotCalculateService.calculateCyclePointNum(query);
            
            long endTime = System.currentTimeMillis();
            long processingTime = endTime - startTime;
            
            // 简单的统计输出（这里需要从calculateCyclePointNum中获取实际统计数据）
            logger.info("抄表记录生成完成！");
            logger.info("  - 处理耗时：{}ms", processingTime);
            logger.info("  - 统计周期：{}", query.getCycle() != null ? query.getCycle() : "上个月");
            logger.info("注意：详细统计数据需要查看上述日志中的具体信息");
            
        } catch (Exception e) {
            logger.error("统计客户端上报打印张数发生异常", e);
            throw e;
        }
        
        logger.info("统计客户端上报打印张数完成------");
    }


    /**
     * 每月1号凌晨8分生成客户收款单数据
     *
     * @throws Exception
     */
//    @ScheduleJob(id = 20005L, cron = "0 8 0 1 * ?", description = "生成客户收款单数据")
    public void generateLastMonthData() throws Exception {
        logger.info("生成客户收款单数据开始------");
        iotPrintReceiptService.generateLastMonthData(new IotPrintCountQuery());
        logger.info("生成客户收款单数据完成------");
    }

    @ScheduleJob(id = 20009L, cron = "0 10 0 ? * * *", description = "每日生成客户分期收款单数据")
    public void statisticsDailyPrintCount() throws Exception {
        List<TradeOrderInstallment> tradeOrderInstallmentList = tradeOrderInstallmentDomainService.lambdaQuery()
                .eq(TradeOrderInstallment::getStatus, 0)
                .eq(TradeOrderInstallment::getPlanPayDate, LocalDate.now())
                .list();
//        iotPrintReceiptService.createInstallmentReceipt(tradeOrderInstallmentList);
    }


}
