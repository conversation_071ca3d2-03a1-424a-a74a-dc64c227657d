package com.hightop.benyin.statistics.api.controller;

import com.hightop.benyin.statistics.api.dto.*;
import com.hightop.benyin.statistics.application.service.FinanceCollectionService;
import com.hightop.benyin.statistics.application.vo.*;
import com.hightop.benyin.statistics.infrastructure.entity.FinanceCollection;
import com.hightop.fario.base.util.StringUtils;
import com.hightop.fario.base.web.Operation;
import com.hightop.fario.base.web.RestResponse;
import com.hightop.fario.common.mybatis.DataGrid;
import com.hightop.magina.standard.behavior.operation.IgnoreOperationLog;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AccessLevel;
import lombok.RequiredArgsConstructor;
import lombok.experimental.FieldDefaults;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;

/**
 * 应收账款统计
 *
 * <AUTHOR>
 * @date 2024-08-16 11:26:37
 */
@RequestMapping("/finance/collection")
@RestController
@FieldDefaults(level = AccessLevel.PRIVATE, makeFinal = true)
@RequiredArgsConstructor
@Api(tags = "财务应收账款")
public class FinanceCollectionController {

    FinanceCollectionService financeCollectionService;
    RedisTemplate<String, String> redisTemplate;

    @PostMapping("/pageList")
    @ApiOperation("应收账款汇总分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<FinanceCollection>> pageList(@RequestBody FinancePayQuery qo) {
        LocalDate localDate = LocalDate.now();
        String yearMonths = localDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        if (StringUtils.isBlank(qo.getYearMonths())) {
            qo.setYearMonths(yearMonths);
        }
        String currMonth = qo.getYearMonths();
        LocalDate startLocalDate = LocalDate.of(Integer.parseInt(currMonth.split("-")[0]),
                Integer.parseInt(currMonth.split("-")[1]), 1);
        LocalDate lastMonthLocaDate = startLocalDate.minusMonths(1);//月份-1
        String lastMonth = lastMonthLocaDate.format(DateTimeFormatter.ofPattern("yyyy-MM"));
        qo.setLastMonths(lastMonth);
        //if (yearMonths.equals(qo.getYearMonths()) && qo.getPageNumber() == 1) {
        if (qo.getPageNumber() == 1){
            String redisKey = "CACHE:COUNT:COLLECTION";
            //一分钟内不重复计算
            //if (!redisTemplate.hasKey(redisKey)) {
                financeCollectionService.recountFinanceCollection(qo);
                redisTemplate.opsForValue().set(redisKey, "1", 1, TimeUnit.MINUTES);
            //}
        }
        return RestResponse.ok(this.financeCollectionService.pageList(qo));
    }

    @PostMapping("/adjust")
    @ApiOperation("调整初期金额")
    public RestResponse<Void> adjustFinancePayment(@RequestBody @Valid FinanceAdjustDto financeAdjustDto) {
        return Operation.UPDATE.response(this.financeCollectionService.adjustFinanceCollection(financeAdjustDto));
    }

    @ApiOperation("/导出应收账款汇总表")
    @PostMapping("/exportFinanceCollection")
    @IgnoreOperationLog
    public RestResponse<Void> exportFinanceCollection(HttpServletResponse response, @RequestBody FinancePayQuery pageQuery) {
        Boolean b = financeCollectionService.downloadFinanceCollection(response, pageQuery);
        if (!b) {
            return new RestResponse<>(500, "导出失败，请联系技术人员！", null, null);
        }
        return RestResponse.message("导出成功");
    }

    @PostMapping("/summary")
    @ApiOperation("应收款机器汇总-统计")
    @IgnoreOperationLog
    public RestResponse<FinanceSummaryVO> getFinancePaySummary(@RequestBody FinancePayQuery qo) {
        return RestResponse.ok(this.financeCollectionService.getFinancePaySummary(qo));
    }

    @PostMapping("/detail/summary")
    @ApiOperation("应收款机器明细-统计")
    @IgnoreOperationLog
    public RestResponse<FinanceSummaryVO> getFinanceDetailSummary(@RequestBody FinanceCollectionQuery qo) {
        return RestResponse.ok(this.financeCollectionService.getFinanceDetailSummary(qo));
    }

    @PostMapping("/materialPageList")
    @ApiOperation("应收款耗材明细表分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<MaterialCollectionVO>> materialPageList(@RequestBody FinanceCollectionQuery qo) {
        return RestResponse.ok(this.financeCollectionService.materialPageList(qo));
    }


    @PostMapping("/receiveCancelVerifyBatch")
    @ApiOperation("批量收款核销验证")
    @IgnoreOperationLog
    public RestResponse<Void> receiveCancelVerifyBatch(@RequestBody FinanceVerifyCancelBatchDto verifyCancelDto) {
        Boolean b = financeCollectionService.receiveCancelVerifyBatch(verifyCancelDto);
        if (!b){
            return new RestResponse<>(500, "核销失败！", null, null);
        }
        return RestResponse.message("核销成功");
    }
    @PostMapping("/receiveCancelVerify")
    @ApiOperation("收款核销验证")
    @IgnoreOperationLog
    public RestResponse<Void> receiveCancelVerify(@RequestBody FinanceVerifyCancelDto verifyCancelDto) {
        Boolean b = financeCollectionService.receiveCancelVerify(verifyCancelDto);
        if (!b){
            return new RestResponse<>(500, "核销失败！", null, null);
        }
        return RestResponse.message("核销成功");
    }

    @PostMapping("/paymentCancelVerifyBatch")
    @ApiOperation("批量付款核销验证")
    @IgnoreOperationLog
    public RestResponse<Void> paymentCancelVerifyBatch(@RequestBody FinanceVerifyCancelBatchDto verifyCancelDto) {
        Boolean b = financeCollectionService.paymentCancelVerifyBatch(verifyCancelDto);
        if (!b){
            return new RestResponse<>(500, "核销失败！", null, null);
        }
        return RestResponse.message("核销成功");
    }

    @PostMapping("/paymentCancelVerify")
    @ApiOperation("付款核销验证")
    @IgnoreOperationLog
    public RestResponse<Void> paymentCancelVerify(@RequestBody FinanceVerifyCancelDto verifyCancelDto) {
        Boolean b = financeCollectionService.paymentCancelVerify(verifyCancelDto);
        if (!b){
            return new RestResponse<>(500, "核销失败！", null, null);
        }
        return RestResponse.message("核销成功");
    }

    @ApiOperation("/导出应收账款耗材明细表")
    @PostMapping("/exportFinanceMaterial")
    @IgnoreOperationLog
    public RestResponse<Void> downloadFinanceMaterial(HttpServletResponse response, @RequestBody FinanceCollectionQuery pageQuery) {
        Boolean b = financeCollectionService.downloadFinanceMaterial(response, pageQuery);
        if (!b) {
            return new RestResponse<>(500, "导出失败，请联系技术人员！", null, null);
        }
        return RestResponse.message("导出成功");
    }

    @PostMapping("/machinePageList")
    @ApiOperation("应收款机器明细表分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<MachineCollectionVO>> machinePageList(@RequestBody FinanceCollectionQuery qo) {
        return RestResponse.ok(this.financeCollectionService.machinePageList(qo));
    }

    @ApiOperation("/导出应收账款机器明细表")
    @PostMapping("/exportFinanceMachine")
    @IgnoreOperationLog
    public RestResponse<Void> downloadFinanceMachine(HttpServletResponse response, @RequestBody FinanceCollectionQuery pageQuery) {
        Boolean b = financeCollectionService.downloadFinanceMachine(response, pageQuery);
        if (!b) {
            return new RestResponse<>(500, "导出失败，请联系技术人员！", null, null);
        }
        return RestResponse.message("导出成功");
    }

    @PostMapping("/operationPageList")
    @ApiOperation("应收账款印张费明细表分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<OperationCollectionVO>> operationPageList(@RequestBody FinanceCollectionQuery qo) {
        return RestResponse.ok(this.financeCollectionService.operationPageList(qo));
    }

    @ApiOperation("/导出应收账款抄表费明细表")
    @PostMapping("/exportFinanceOperation")
    @IgnoreOperationLog
    public RestResponse<Void> downloadFinanceOpeation(HttpServletResponse response, @RequestBody FinanceCollectionQuery pageQuery) {
        Boolean b = financeCollectionService.downloadFinanceOpeation(response, pageQuery);
        if (!b) {
            return new RestResponse<>(500, "导出失败，请联系技术人员！", null, null);
        }
        return RestResponse.message("导出成功");
    }

    @PostMapping("/repairPageList")
    @ApiOperation("应收账款维修费明细表分页查询")
    @IgnoreOperationLog
    public RestResponse<DataGrid<RepairCollectionVO>> repairPageList(@RequestBody FinanceCollectionQuery qo) {
        return RestResponse.ok(this.financeCollectionService.repairPageList(qo));
    }

    @ApiOperation("/导出应收账款维修费明细表")
    @PostMapping("/exportFinanceRepair")
    @IgnoreOperationLog
    public RestResponse<Void> downloadFinanceRepair(HttpServletResponse response, @RequestBody FinanceCollectionQuery pageQuery) {
        Boolean b = financeCollectionService.downloadFinanceRepair(response, pageQuery);
        if (!b) {
            return new RestResponse<>(500, "导出失败，请联系技术人员！", null, null);
        }
        return RestResponse.message("导出成功");
    }

}
